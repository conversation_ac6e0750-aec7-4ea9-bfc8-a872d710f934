#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非正六边形卷针薄膜包覆动态仿真 (简化版)
图像描述：固定过辊在右上方，六边形卷针在下方旋转

参数设置说明:
- 过辊位置A: 在下面代码中直接修改 self.A = np.array([x, y])
- 旋转中心X偏移: 默认2.0mm，可在初始化时设置或运行时调整
- 六边形顶点: 可在 self.vertices 中直接修改坐标
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Polygon

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class HexagonFilmSimulation:
    def __init__(self, rotation_center_x_offset=2.0, film_thickness=0.1):
        """
        初始化仿真参数

        Parameters:
        rotation_center_x_offset: float
            旋转中心在X方向相对于几何中心的偏移量 (默认2.0mm)
            Y方向保持几何中心不变
        film_thickness: float
            隔膜厚度 (默认0.1mm)
        """

        # ==================== 核心参数设置区域 ====================
        # 过辊位置 (如需修改，直接改下面的数值)
        self.A = np.array([0.5, 80.0])  # [X坐标, Y坐标] - 可直接修改

        # 过辊参数
        self.roller_radius = 2.0  # 过辊半径 R=2

        # 隔膜厚度参数
        self.film_thickness = film_thickness  # 单层隔膜厚度
        self.current_thickness = 0.0  # 当前累积厚度
        self.layers_count = 0  # 当前层数

        # 非正六边形顶点坐标 (逆时针顺序) - 初始状态，无厚度
        self.original_vertices = np.array(
            [
                [-30, 0],  # V1 (初始接触点 - "内角")
                [-20, -4],  # V2
                [20, -4],  # V3
                [30, 0],  # V4
                [20, 4],  # V5
                [-20, 4],  # V6
            ]
        )

        # 当前顶点坐标（会随厚度变化）
        self.vertices = self.original_vertices.copy()

        # 旋转中心设置 (自动计算)
        self.geometric_center = np.mean(self.vertices, axis=0)  # 几何中心

        # 新增：旋转中心上下两个特殊点位
        self.upper_point = np.array(
            [self.geometric_center[0] + rotation_center_x_offset, 4]
        )  # 旋转中心上方点
        self.lower_point = np.array(
            [self.geometric_center[0] + rotation_center_x_offset, -4]
        )  # 旋转中心下方点
        # ========================================================

        self.rotation_center_x_offset = rotation_center_x_offset  # X方向偏移
        self.rotation_center = np.array(
            [
                self.geometric_center[0]
                + rotation_center_x_offset,  # X = 几何中心X + 偏移
                self.geometric_center[1],  # Y = 几何中心Y (不变)
            ]
        )

        # 旋转参数
        self.total_rotation = 3600  # 总旋转角度 (度)
        self.step_angle = 0.5  # 旋转步长 (度)
        self.theta_deg = np.arange(
            0, self.total_rotation + self.step_angle, self.step_angle
        )
        self.theta = np.deg2rad(self.theta_deg)

        # 初始化结果数组
        self.L = np.zeros(len(self.theta))  # 过辊到卷针长度
        self.S = np.zeros(len(self.theta))  # 包覆长度（仅卷针表面）
        self.S_total = np.zeros(len(self.theta))  # 总薄膜长度（切线+包覆）
        self.contact_points = np.zeros((len(self.theta), 2))  # 接触点坐标
        self.contact_type = [""] * len(self.theta)  # 接触类型 (边/顶点)

        # 新增：过辊接触点相关数组
        self.roller_contact_points = np.zeros(
            (len(self.theta), 2)
        )  # 过辊上的接触点坐标
        self.roller_contact_distances = np.zeros(
            len(self.theta)
        )  # 接触点到过辊接触点的距离

        # 新增：特殊点位的旋转轨迹
        self.upper_point_trajectory = np.zeros((len(self.theta), 2))  # 上方点轨迹
        self.lower_point_trajectory = np.zeros((len(self.theta), 2))  # 下方点轨迹

        # 新增：厚度相关数组
        self.layer_numbers = np.zeros(len(self.theta), dtype=int)  # 每个角度对应的层数
        self.accumulated_thickness = np.zeros(len(self.theta))  # 累积厚度
        self.layer_consumption = np.zeros(len(self.theta))  # 每层消耗长度

        # 计算初始状态下A点到上方点的连线，用于验证初始连接
        self.initial_connection_length = np.linalg.norm(self.A - self.upper_point)
        print(
            f"初始状态：A点({self.A[0]:.1f}, {self.A[1]:.1f})到上方点({self.upper_point[0]:.1f}, {self.upper_point[1]:.1f})的距离: {self.initial_connection_length:.2f}"
        )
        print(f"隔膜厚度设置: {self.film_thickness:.2f} mm/层")

    def rotation_matrix(self, theta):
        """创建旋转矩阵"""
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        return np.array([[cos_theta, -sin_theta], [sin_theta, cos_theta]])

    def rotate_vertices_around_center(self, theta):
        """
        绕指定旋转中心旋转六边形顶点

        Parameters:
        theta: 旋转角度 (弧度)

        Returns:
        rotated_vertices: 旋转后的顶点坐标
        """
        # 步骤1: 平移到旋转中心为原点
        centered_vertices = self.vertices - self.rotation_center

        # 步骤2: 应用旋转矩阵
        R = self.rotation_matrix(theta)
        rotated_centered = (R @ centered_vertices.T).T

        # 步骤3: 平移回原来的位置
        rotated_vertices = rotated_centered + self.rotation_center

        return rotated_vertices

    def rotate_vertices_around_center_with_custom_vertices(
        self, theta, custom_vertices
    ):
        """
        绕指定旋转中心旋转自定义顶点（用于厚度更新后的顶点）

        Parameters:
        theta: 旋转角度 (弧度)
        custom_vertices: 自定义顶点坐标数组

        Returns:
        rotated_vertices: 旋转后的顶点坐标
        """
        # 步骤1: 平移到旋转中心为原点
        centered_vertices = custom_vertices - self.rotation_center

        # 步骤2: 应用旋转矩阵
        R = self.rotation_matrix(theta)
        rotated_centered = (R @ centered_vertices.T).T

        # 步骤3: 平移回原来的位置
        rotated_vertices = rotated_centered + self.rotation_center

        return rotated_vertices

    def rotate_special_points_around_center(self, theta):
        """
        绕指定旋转中心旋转特殊点位（上方点和下方点）

        Parameters:
        theta: 旋转角度 (弧度)

        Returns:
        rotated_upper_point: 旋转后的上方点坐标
        rotated_lower_point: 旋转后的下方点坐标
        """
        # 旋转上方点
        centered_upper = self.upper_point - self.rotation_center
        R = self.rotation_matrix(theta)
        rotated_upper_centered = R @ centered_upper
        rotated_upper_point = rotated_upper_centered + self.rotation_center

        # 旋转下方点
        centered_lower = self.lower_point - self.rotation_center
        rotated_lower_centered = R @ centered_lower
        rotated_lower_point = rotated_lower_centered + self.rotation_center

        return rotated_upper_point, rotated_lower_point

    def update_vertices_with_thickness(self, accumulated_thickness):
        """
        根据累积厚度更新六边形顶点位置
        厚度累积会使六边形向外扩展

        Parameters:
        accumulated_thickness: float
            累积的隔膜厚度
        """
        # 计算几何中心
        center = np.mean(self.original_vertices, axis=0)

        # 对每个顶点，沿着从中心到顶点的方向向外扩展
        updated_vertices = []
        for vertex in self.original_vertices:
            # 从中心到顶点的向量
            to_vertex = vertex - center
            # 向量长度（距离）
            distance = np.linalg.norm(to_vertex)

            if distance > 1e-10:  # 避免除零
                # 单位向量
                unit_vector = to_vertex / distance
                # 新的顶点位置：原位置 + 厚度方向的扩展
                new_vertex = vertex + unit_vector * accumulated_thickness
                updated_vertices.append(new_vertex)
            else:
                updated_vertices.append(vertex)

        self.vertices = np.array(updated_vertices)
        return self.vertices

    def calculate_layer_from_angle(self, angle_deg):
        """
        根据旋转角度计算当前层数
        每360度为一层

        Parameters:
        angle_deg: float
            当前旋转角度（度）

        Returns:
        layer_number: int
            当前层数（从0开始）
        accumulated_thickness: float
            累积厚度
        """
        layer_number = int(angle_deg // 360)
        accumulated_thickness = layer_number * self.film_thickness
        return layer_number, accumulated_thickness

    def get_film_consumption_per_layer(self, layer_number):
        """
        计算每层的隔膜消耗长度
        随着层数增加，每层消耗的隔膜长度会增加

        Parameters:
        layer_number: int
            层数（从0开始）

        Returns:
        consumption: float
            该层的隔膜消耗长度
        """
        # 基础周长（第0层）
        base_perimeter = 0
        for i in range(len(self.original_vertices)):
            v1 = self.original_vertices[i]
            v2 = self.original_vertices[(i + 1) % len(self.original_vertices)]
            base_perimeter += np.linalg.norm(v2 - v1)

        # 考虑厚度的周长增加
        # 假设每层厚度增加导致周长按比例增加
        thickness_factor = 1 + (layer_number * self.film_thickness) / 10.0  # 经验公式
        layer_consumption = base_perimeter * thickness_factor

        return layer_consumption

    def set_rotation_center_x_offset(self, x_offset):
        """
        设置旋转中心X方向偏移量

        Parameters:
        x_offset: float - X方向偏移量 (mm)
        """
        self.rotation_center_x_offset = x_offset
        self.rotation_center = np.array(
            [self.geometric_center[0] + x_offset, self.geometric_center[1]]
        )
        self.upper_point = np.array(
            [self.geometric_center[0] + x_offset, 4]
        )  # 旋转中心上方点
        self.lower_point = np.array(
            [self.geometric_center[0] + x_offset, -4]
        )  # 旋转中心下方点
        print(f"旋转中心X偏移已设置为: {x_offset:.2f} mm")
        print(
            f"旋转中心位置: ({self.rotation_center[0]:.2f}, {self.rotation_center[1]:.2f})"
        )

    def find_contact_point(self, A, vertices):
        """
        寻找薄膜与六边形的真实接触点
        考虑薄膜包覆的物理约束：切线几何 + 包覆方向
        """
        n = len(vertices)

        # 寻找从过辊A到六边形的所有可能切点
        tangent_points = []
        tangent_info = []

        # 1. 检查每条边的切线
        for k in range(n):
            p1 = vertices[k]
            p2 = vertices[(k + 1) % n]

            # 计算边的方向向量和法向量
            edge_vec = p2 - p1
            edge_length = np.linalg.norm(edge_vec)

            if edge_length < 1e-10:
                continue

            unit_edge = edge_vec / edge_length
            normal = np.array([-unit_edge[1], unit_edge[0]])  # 垂直于边的法向量

            # 从A到边的向量
            ap_vec = A - p1

            # 计算投影点
            proj_length = np.dot(ap_vec, unit_edge)

            if 0 <= proj_length <= edge_length:
                # 投影点在边上
                proj_point = p1 + proj_length * unit_edge

                # 检查是否为有效切点（薄膜不能穿过六边形）
                to_proj = proj_point - A

                # 检查切线方向是否合理（切线应该"切"到多边形外侧）
                cross_product = np.cross(to_proj, normal)

                tangent_points.append(proj_point)
                tangent_info.append(
                    {
                        "point": proj_point,
                        "edge": k,
                        "type": "edge",
                        "distance": np.linalg.norm(to_proj),
                        "cross": cross_product,
                    }
                )

        # 2. 检查每个顶点作为切点的可能性
        for k in range(n):
            vertex = vertices[k]
            to_vertex = vertex - A
            dist = np.linalg.norm(to_vertex)

            # 检查顶点切线的几何有效性
            prev_edge = vertices[k] - vertices[(k - 1) % n]
            next_edge = vertices[(k + 1) % n] - vertices[k]

            # 计算顶点处的内角
            angle = np.arccos(
                np.clip(
                    np.dot(-prev_edge, next_edge)
                    / (np.linalg.norm(prev_edge) * np.linalg.norm(next_edge)),
                    -1,
                    1,
                )
            )

            tangent_points.append(vertex)
            tangent_info.append(
                {
                    "point": vertex,
                    "edge": k,
                    "type": "vertex",
                    "distance": dist,
                    "angle": angle,
                }
            )

        if not tangent_info:
            # 如果没有找到切点，返回最近顶点
            distances = [np.linalg.norm(A - v) for v in vertices]
            min_idx = np.argmin(distances)
            return vertices[min_idx], min_idx, distances

        # 3. 选择物理上正确的接触点
        # 根据薄膜包覆的物理约束选择接触点

        # 首先按距离排序
        tangent_info.sort(key=lambda x: x["distance"])

        # 选择满足包覆约束的接触点
        # 对于入卷，接触点应该是薄膜"最后离开"的点
        # 这通常是X坐标最小（最左）的有效切点

        valid_points = []
        for info in tangent_info:
            point = info["point"]

            # 检查几何约束：薄膜不能穿过多边形内部
            if self.is_valid_tangent_point(A, point, vertices):
                valid_points.append(info)

        if not valid_points:
            valid_points = tangent_info[:1]  # 至少选择最近的点

        # 在有效点中选择X坐标最小的（最左边的点）
        # 这符合"入卷的最后接触点位置肯定是左侧的X最左边的那个点"
        leftmost_point = min(valid_points, key=lambda x: x["point"][0])

        contact = leftmost_point["point"]
        current_edge = leftmost_point["edge"]

        # 计算到各顶点的距离（保持接口兼容）
        dist_to_vertex = np.array([np.linalg.norm(contact - v) for v in vertices])

        return contact, current_edge, dist_to_vertex

    def is_valid_tangent_point(self, A, point, vertices):
        """
        检查切点是否物理有效
        薄膜不能穿过多边形内部
        """
        # 检查从A到point的直线是否与多边形的其他边相交
        n = len(vertices)

        for k in range(n):
            p1 = vertices[k]
            p2 = vertices[(k + 1) % n]

            # 检查线段AP与边P1P2是否相交（除了在端点）
            if self.line_segments_intersect(A, point, p1, p2):
                # 如果相交点不是切点本身，则无效
                intersection = self.get_intersection_point(A, point, p1, p2)
                if intersection is not None:
                    dist_to_point = np.linalg.norm(intersection - point)
                    if dist_to_point > 1e-6:  # 不是同一点
                        return False

        return True

    def line_segments_intersect(self, p1, p2, p3, p4):
        """检查两条线段是否相交"""

        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])

        return (ccw(p1, p3, p4) != ccw(p2, p3, p4)) and (
            ccw(p1, p2, p3) != ccw(p1, p2, p4)
        )

    def get_intersection_point(self, p1, p2, p3, p4):
        """计算两条直线的交点"""
        x1, y1 = p1
        x2, y2 = p2
        x3, y3 = p3
        x4, y4 = p4

        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return None

        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom

        intersection = np.array([x1 + t * (x2 - x1), y1 + t * (y2 - y1)])
        return intersection

    def calc_tangent_points(self, cx, cy, radius, outside_point):
        """
        计算从外部点到圆的切点
        基于JavaScript代码的Python实现

        Parameters:
        cx, cy: float
            圆心坐标
        radius: float
            圆的半径
        outside_point: np.array
            外部点坐标 [x, y]

        Returns:
        tangent_points: list of np.array
            切点坐标列表，通常有2个切点
        """
        # 将实际的点做一次转换，因为下面的计算都是按圆心在原点计算的
        outside_x = outside_point[0] - cx
        outside_y = outside_point[1] - cy

        # 计算m值
        m = (outside_x**2 + outside_y**2) / (radius**2)

        if m <= 1:
            # 点在圆内或圆上，无切点
            return []

        # 计算sqrt(m-1)
        sqrt_m_minus_1 = np.sqrt(m - 1)

        # 求出的结果将会有4种排列
        point_a = np.array(
            [
                (outside_x + outside_y * sqrt_m_minus_1) / m,
                (outside_y + outside_x * sqrt_m_minus_1) / m,
            ]
        )

        point_b = np.array(
            [
                (outside_x - outside_y * sqrt_m_minus_1) / m,
                (outside_y - outside_x * sqrt_m_minus_1) / m,
            ]
        )

        point_c = np.array(
            [
                (outside_x + outside_y * sqrt_m_minus_1) / m,
                (outside_y - outside_x * sqrt_m_minus_1) / m,
            ]
        )

        point_d = np.array(
            [
                (outside_x - outside_y * sqrt_m_minus_1) / m,
                (outside_y + outside_x * sqrt_m_minus_1) / m,
            ]
        )

        # 实际上只会有2个切点，利用向量垂直，点乘结果是0来判断哪个是有效的
        # 因为浮点数不能精确到0，所以这里用了1e-10
        valid_points = []
        tolerance = 1e-10

        for point in [point_a, point_b, point_c, point_d]:
            # 检查切线条件：从外部点到切点的向量与从切点到圆心的向量垂直
            to_tangent = point - np.array([outside_x, outside_y])
            to_center = np.array([0, 0]) - point  # 圆心在原点

            dot_product = np.dot(to_tangent, to_center)

            if abs(dot_product) <= tolerance:
                # 将坐标转换回原来的坐标系
                actual_point = point + np.array([cx, cy])
                valid_points.append(actual_point)

        return valid_points

    def find_roller_contact_point(self, contact_point):
        """
        计算从接触点到过辊的切点
        选择在A点左侧的切点（x > 0.5的点）

        Parameters:
        contact_point: np.array
            当前的接触点坐标

        Returns:
        roller_contact_point: np.array
            过辊上的接触点坐标
        """
        # 计算从接触点到过辊圆心的切点
        tangent_points = self.calc_tangent_points(
            self.A[0], self.A[1], self.roller_radius, contact_point
        )

        if not tangent_points:
            # 如果没有切点，直接返回过辊圆心
            return self.A

        # 选择在A点左侧的切点（x坐标大于0.5的点）
        valid_points = []
        for point in tangent_points:
            if point[0] > 0.5:  # x > 0.5
                valid_points.append(point)

        if not valid_points:
            # 如果没有满足条件的切点，选择最接近的
            return tangent_points[0]

        # 如果有多个满足条件的点，选择x坐标最大的（最左侧）
        leftmost_point = max(valid_points, key=lambda p: p[0])
        return leftmost_point

    def distance_along_polygon(self, prev_point, current_point, vertices):
        """计算多边形边界上的距离"""
        # 简单方法 - 直接计算两点间直线距离(小角度近似)
        return np.linalg.norm(current_point - prev_point)

    def find_leftmost_valid_contact_point(
        self, A, rotated_vertices, rotated_upper, angle_deg
    ):
        """
        根据正确的包覆规则寻找接触点：
        1. 初始状态：固定连接到(2,4)点
        2. 包覆规则：总是连接到最左侧的点（X坐标最小）
        3. 限制条件：旋转超过90度前，不能包括V1、V2、V6，但要包括(2,4)点
        4. 包覆顺序：根据最左侧原则决定
        """
        # 初始状态：固定连接到(2,4)点
        if angle_deg == 0:
            return rotated_upper, "fixed_upper_point"

        # 收集候选点
        candidate_points = []
        candidate_info = []

        if angle_deg < 180:
            # 旋转小于90度：考虑V3、V4、V5（索引2、3、4）+ (2,4)点
            valid_vertex_indices = [2, 3, 4]  # V3, V4, V5

            # 添加六边形顶点
            for idx in valid_vertex_indices:
                vertex = rotated_vertices[idx]
                candidate_points.append(vertex)
                candidate_info.append(
                    {"point": vertex, "type": f"vertex_V{idx + 1}", "x": vertex[0]}
                )

            # 添加旋转后的(2,4)点
            candidate_points.append(rotated_upper)
            candidate_info.append(
                {"point": rotated_upper, "type": "upper_point", "x": rotated_upper[0]}
            )

        else:
            # 旋转超过90度：考虑所有顶点
            for idx in range(len(rotated_vertices)):
                vertex = rotated_vertices[idx]
                candidate_points.append(vertex)
                candidate_info.append(
                    {"point": vertex, "type": f"vertex_V{idx + 1}", "x": vertex[0]}
                )

        # 在所有候选点中寻找最左侧的点（X坐标最小）
        if not candidate_info:
            return rotated_upper, "upper_point"

        leftmost_candidate = min(candidate_info, key=lambda x: x["x"])
        leftmost_point = leftmost_candidate["point"]
        leftmost_type = leftmost_candidate["type"]

        # 返回最左侧的点
        if leftmost_type == "upper_point":
            return leftmost_point, "leftmost_upper_point"
        elif leftmost_type.startswith("vertex_"):
            vertex_name = leftmost_type.replace("vertex_", "")
            return leftmost_point, f"leftmost_{vertex_name}"
        else:
            return leftmost_point, leftmost_type

    def run_simulation(self):
        """运行主仿真循环（支持隔膜厚度计算）"""
        print("开始仿真...")
        print(
            f"旋转中心: ({self.rotation_center[0]:.2f}, {self.rotation_center[1]:.2f})"
        )
        print(
            f"几何中心: ({self.geometric_center[0]:.2f}, {self.geometric_center[1]:.2f})"
        )
        print(f"X方向偏移: {self.rotation_center_x_offset:.2f} mm")
        print(f"上方点初始位置: ({self.upper_point[0]:.2f}, {self.upper_point[1]:.2f})")
        print(f"下方点初始位置: ({self.lower_point[0]:.2f}, {self.lower_point[1]:.2f})")
        print(f"隔膜厚度: {self.film_thickness:.2f} mm/层")
        print(
            f"初始薄膜路径: A({self.A[0]:.1f}, {self.A[1]:.1f}) -> 上方点({self.upper_point[0]:.1f}, {self.upper_point[1]:.1f})"
        )

        for i in range(len(self.theta)):
            if i % 1000 == 0:
                print(f"进度: {i / len(self.theta) * 100:.1f}%")

            # 计算当前层数和累积厚度
            layer_num, accum_thickness = self.calculate_layer_from_angle(
                self.theta_deg[i]
            )
            self.layer_numbers[i] = layer_num
            self.accumulated_thickness[i] = accum_thickness

            # 根据累积厚度更新顶点位置
            if accum_thickness > 0:
                current_vertices = self.update_vertices_with_thickness(accum_thickness)
            else:
                current_vertices = self.vertices.copy()

            # 使用更新后的顶点进行旋转
            rotated_vertices = self.rotate_vertices_around_center_with_custom_vertices(
                self.theta[i], current_vertices
            )

            # 计算特殊点位的旋转轨迹
            rotated_upper, rotated_lower = self.rotate_special_points_around_center(
                self.theta[i]
            )
            self.upper_point_trajectory[i] = rotated_upper
            self.lower_point_trajectory[i] = rotated_lower

            # 使用最左侧点包覆规则寻找接触点
            contact, contact_type = self.find_leftmost_valid_contact_point(
                self.A, rotated_vertices, rotated_upper, self.theta_deg[i]
            )

            self.contact_points[i] = contact
            self.contact_type[i] = contact_type

            # 计算过辊接触点
            roller_contact = self.find_roller_contact_point(contact)
            self.roller_contact_points[i] = roller_contact

            # 计算接触点到过辊接触点的距离
            self.roller_contact_distances[i] = np.linalg.norm(contact - roller_contact)

            # 计算过辊到接触点的长度（通过过辊接触点）
            self.L[i] = np.linalg.norm(self.A - contact)

            # 计算每层的隔膜消耗长度
            if layer_num > 0:
                self.layer_consumption[i] = self.get_film_consumption_per_layer(
                    layer_num
                )
            else:
                self.layer_consumption[i] = 0.0

            # 计算包覆长度 (仅沿六边形边界的部分)
            if i > 0:
                self.S[i] = self.S[i - 1] + self.distance_along_polygon(
                    self.contact_points[i - 1], self.contact_points[i], rotated_vertices
                )
            else:
                self.S[i] = 0.0  # 初始包覆长度为0

            # 计算总薄膜长度 (从过辊切点开始的长度 + 覆盖在卷针上的长度)
            tangent_length = self.roller_contact_distances[
                i
            ]  # 从过辊切点到接触点的距离
            surface_coverage = self.S[i]  # 覆盖在卷针表面的长度
            self.S_total[i] = tangent_length + surface_coverage

        print("仿真完成!")
        print(
            f"特殊点位轨迹计算完成，上方点最终位置: ({self.upper_point_trajectory[-1][0]:.2f}, {self.upper_point_trajectory[-1][1]:.2f})"
        )
        print(
            f"下方点最终位置: ({self.lower_point_trajectory[-1][0]:.2f}, {self.lower_point_trajectory[-1][1]:.2f})"
        )
        print(f"最大层数: {np.max(self.layer_numbers)}")
        print(f"最大累积厚度: {np.max(self.accumulated_thickness):.2f} mm")
        print(f"总隔膜消耗长度: {np.sum(self.layer_consumption):.2f} mm")
        print("过辊接触点统计:")
        print(
            f"  切线长度 - 最大: {np.max(self.roller_contact_distances):.2f}, 最小: {np.min(self.roller_contact_distances):.2f}"
        )
        print(f"  平均切线长度: {np.mean(self.roller_contact_distances):.2f} mm")

    def visualize_contact_analysis(self, angle_deg=0):
        """
        可视化接触点分析，验证算法正确性
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

        # 使用新的旋转方法
        theta = np.deg2rad(angle_deg)
        rotated_vertices = self.rotate_vertices_around_center(theta)
        rotated_upper, rotated_lower = self.rotate_special_points_around_center(theta)

        # 使用最左侧点包覆规则计算接触点
        contact, contact_type = self.find_leftmost_valid_contact_point(
            self.A, rotated_vertices, rotated_upper, angle_deg
        )

        # 计算过辊接触点
        roller_contact = self.find_roller_contact_point(contact)

        # if contact_type == "fixed_upper_point":
        #     contact_type_display = "固定(2,4)点"
        # elif contact_type == "leftmost_upper_point":
        #     contact_type_display = "最左侧: (2,4)点"
        # elif contact_type == "upper_point":
        #     contact_type_display = "上方点连接"
        # elif contact_type.startswith("leftmost_"):
        #     vertex_name = contact_type.replace("leftmost_", "")
        #     contact_type_display = f"最左侧点: {vertex_name}"
        # else:
        #     contact_type_display = contact_type

        # 左图：详细的接触点分析
        ax1.set_aspect("equal")
        ax1.grid(True, alpha=0.3)
        ax1.set_title(
            f"接触点分析 (角度: {angle_deg}°)\n旋转中心X偏移: {self.rotation_center_x_offset:.1f} mm\n含特殊点位",
            fontsize=14,
            fontweight="bold",
        )

        # 绘制过辊圆心
        ax1.plot(
            self.A[0],
            self.A[1],
            "bo",
            markersize=15,
            markerfacecolor="blue",
            markeredgecolor="darkblue",
            markeredgewidth=2,
            label="过辊圆心A",
        )

        # 绘制过辊圆
        roller_circle = plt.Circle(
            (self.A[0], self.A[1]),
            self.roller_radius,
            fill=False,
            color="blue",
            linewidth=2,
            alpha=0.7,
            label="过辊圆",
        )
        ax1.add_patch(roller_circle)

        # 绘制几何中心和旋转中心
        ax1.plot(
            self.geometric_center[0],
            self.geometric_center[1],
            "gs",
            markersize=12,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            label="几何中心",
        )
        ax1.plot(
            self.rotation_center[0],
            self.rotation_center[1],
            "r^",
            markersize=12,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            label="旋转中心",
        )

        # 绘制特殊点位（旋转后的位置）
        ax1.plot(
            rotated_upper[0],
            rotated_upper[1],
            "mo",
            markersize=12,
            markerfacecolor="magenta",
            markeredgecolor="darkmagenta",
            markeredgewidth=2,
            label="上方点(2,4)",
        )
        ax1.plot(
            rotated_lower[0],
            rotated_lower[1],
            "co",
            markersize=12,
            markerfacecolor="cyan",
            markeredgecolor="darkcyan",
            markeredgewidth=2,
            label="下方点(-2,-4)",
        )

        # 绘制薄膜路径 - 根据接触类型决定连接方式和颜色
        if contact_type == "fixed_upper_point":
            # 初始状态：固定连接到(2,4)点
            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                "m-",
                linewidth=5,
                alpha=0.9,
                label="薄膜路径：A→固定(2,4)点",
            )
            # 添加黄色内芯突出显示
            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                "yellow",
                linewidth=3,
                alpha=0.8,
            )
            path_color = "magenta"
        elif contact_type == "leftmost_upper_point":
            # 薄膜连接到最左侧的(2,4)点
            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                "purple",
                linewidth=5,
                alpha=0.9,
                label="薄膜路径：A→(2,4)点(最左侧)",
            )
            # 添加白色内芯突出显示
            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                "white",
                linewidth=3,
                alpha=0.8,
            )
            path_color = "purple"
        elif contact_type == "upper_point":
            # 薄膜连接到旋转的上方点
            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                "m-",
                linewidth=4,
                alpha=0.9,
                label="薄膜路径：A→上方点",
            )
            path_color = "magenta"
        elif contact_type.startswith("leftmost_"):
            # 薄膜包覆到最左侧顶点
            vertex_name = contact_type.replace("leftmost_", "")
            if vertex_name == "V5":
                color = "orange"
                label = f"薄膜路径：A→{vertex_name}(最左侧)"
            elif vertex_name == "V4":
                color = "red"
                label = f"薄膜路径：A→{vertex_name}(最左侧)"
            elif vertex_name == "V3":
                color = "blue"
                label = f"薄膜路径：A→{vertex_name}(最左侧)"
            elif vertex_name == "V1":
                color = "darkgreen"
                label = f"薄膜路径：A→{vertex_name}(最左侧)"
            else:
                color = "green"
                label = f"薄膜路径：A→{vertex_name}(最左侧)"

            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                color=color,
                linewidth=4,
                alpha=0.9,
                label=label,
            )
            path_color = color
        else:
            # 其他情况
            ax1.plot(
                [self.A[0], contact[0]],
                [self.A[1], contact[1]],
                "gray",
                linewidth=4,
                alpha=0.9,
                label=f"薄膜路径：{contact_type}",
            )
            path_color = "gray"

        # 标注薄膜长度和接触类型
        film_length = np.linalg.norm(self.A - contact)
        mid_x = (self.A[0] + contact[0]) / 2
        mid_y = (self.A[1] + contact[1]) / 2

        # 根据接触类型显示不同信息
        if contact_type == "fixed_upper_point":
            info_text = f"L={film_length:.1f}\n固定(2,4)点"
            bg_color = "yellow"
        elif contact_type == "leftmost_upper_point":
            info_text = f"L={film_length:.1f}\n最左侧: (2,4)点"
            bg_color = "lavender"
        elif contact_type.startswith("leftmost_"):
            vertex_name = contact_type.replace("leftmost_", "")
            info_text = f"L={film_length:.1f}\n最左侧: {vertex_name}"
            bg_color = "lightblue"
        else:
            info_text = f"L={film_length:.1f}\n{contact_type}"
            bg_color = "white"

        ax1.text(
            mid_x + 3,
            mid_y,
            info_text,
            fontsize=10,
            color=path_color,
            fontweight="bold",
            bbox=dict(boxstyle="round", facecolor=bg_color, alpha=0.8),
        )

        # 绘制中心连线
        ax1.plot(
            [self.geometric_center[0], self.rotation_center[0]],
            [self.geometric_center[1], self.rotation_center[1]],
            "k--",
            alpha=0.5,
            linewidth=2,
            label=f"X偏移: {self.rotation_center_x_offset:.1f}mm",
        )

        # 绘制六边形
        hex_x = np.append(rotated_vertices[:, 0], rotated_vertices[0, 0])
        hex_y = np.append(rotated_vertices[:, 1], rotated_vertices[0, 1])
        ax1.plot(hex_x, hex_y, "k-", linewidth=3, label="六边形卷针")

        # 标注顶点
        for i, vertex in enumerate(rotated_vertices):
            ax1.plot(vertex[0], vertex[1], "ko", markersize=8)
            ax1.text(
                vertex[0] + 1,
                vertex[1] + 1,
                f"V{i + 1}",
                fontsize=10,
                fontweight="bold",
            )

        # 绘制过辊接触点
        ax1.plot(
            roller_contact[0],
            roller_contact[1],
            "go",
            markersize=10,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            label="过辊接触点",
        )

        # 绘制从接触点到过辊接触点的切线
        ax1.plot(
            [contact[0], roller_contact[0]],
            [contact[1], roller_contact[1]],
            "g--",
            linewidth=3,
            alpha=0.8,
            label="隔膜路径（切线）",
        )

        # 突出显示最终选择的接触点
        ax1.plot(
            [self.A[0], contact[0]],
            [self.A[1], contact[1]],
            "r-",
            linewidth=4,
            label="薄膜路径",
        )
        ax1.plot(
            contact[0],
            contact[1],
            "ro",
            markersize=12,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            label="接触点",
        )

        # 标注接触点坐标
        ax1.text(
            contact[0] + 2,
            contact[1] + 2,
            f"接触点\n({contact[0]:.1f}, {contact[1]:.1f})",
            fontsize=10,
            fontweight="bold",
            color="red",
            bbox=dict(boxstyle="round", facecolor="yellow", alpha=0.8),
        )

        # 标注过辊接触点坐标
        ax1.text(
            roller_contact[0] + 2,
            roller_contact[1] - 5,
            f"过辊接触点\n({roller_contact[0]:.1f}, {roller_contact[1]:.1f})",
            fontsize=10,
            fontweight="bold",
            color="green",
            bbox=dict(boxstyle="round", facecolor="lightgreen", alpha=0.8),
        )

        # 标注切线长度
        tangent_length = np.linalg.norm(contact - roller_contact)
        mid_x = (contact[0] + roller_contact[0]) / 2
        mid_y = (contact[1] + roller_contact[1]) / 2
        ax1.text(
            mid_x,
            mid_y + 2,
            f"切线长度: {tangent_length:.2f}",
            fontsize=9,
            fontweight="bold",
            color="green",
            bbox=dict(boxstyle="round", facecolor="lightcyan", alpha=0.8),
        )

        ax1.set_xlim(-50, 50)
        ax1.set_ylim(-20, 90)
        ax1.legend()

        # 右图：X坐标分析
        ax2.set_title("X坐标分析 - 验证左侧优先原则", fontsize=14, fontweight="bold")
        ax2.grid(True, alpha=0.3)

        # 分析所有可能接触点的X坐标
        x_coords = []
        labels = []

        # 顶点X坐标
        for i, vertex in enumerate(rotated_vertices):
            x_coords.append(vertex[0])
            labels.append(f"V{i + 1}")

        # 绘制X坐标分布
        y_pos = range(len(x_coords))
        bars = ax2.barh(y_pos, x_coords, alpha=0.7)

        # 突出显示选中的接触点
        contact_x = contact[0]
        min_x_idx = np.argmin(np.abs(np.array(x_coords) - contact_x))
        bars[min_x_idx].set_color("red")
        bars[min_x_idx].set_alpha(1.0)

        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(labels)
        ax2.set_xlabel("X坐标")
        ax2.axvline(
            contact_x,
            color="red",
            linestyle="--",
            linewidth=2,
            label=f"选中点 X={contact_x:.1f}",
        )

        # 标注最左点
        min_x = min(x_coords)
        ax2.axvline(
            min_x,
            color="green",
            linestyle="--",
            linewidth=2,
            label=f"最左点 X={min_x:.1f}",
        )

        ax2.legend()

        plt.tight_layout()
        plt.show()

        # 打印分析结果
        print(f"\n接触点分析结果 (角度: {angle_deg}°):")
        print(f"旋转中心X偏移: {self.rotation_center_x_offset:.2f} mm")
        print(f"选择的接触点: ({contact[0]:.2f}, {contact[1]:.2f})")
        print(f"过辊接触点: ({roller_contact[0]:.2f}, {roller_contact[1]:.2f})")
        print(f"切线长度: {np.linalg.norm(contact - roller_contact):.2f}")
        print(f"接触点X坐标: {contact[0]:.2f}")
        print(f"最左侧X坐标: {min(x_coords):.2f}")
        print(f"是否选择了最左点: {abs(contact[0] - min(x_coords)) < 1e-3}")
        print(f"过辊接触点是否在左侧 (x>0.5): {roller_contact[0] > 0.5}")

    def compare_rotation_centers(self, x_offsets_list):
        """
        比较不同旋转中心X偏移的效果

        Parameters:
        x_offsets_list: list of floats [x1, x2, x3, ...]
            不同的旋转中心X方向偏移量
        """
        fig, axes = plt.subplots(
            2, len(x_offsets_list), figsize=(5 * len(x_offsets_list), 10)
        )
        if len(x_offsets_list) == 1:
            axes = axes.reshape(2, 1)

        results = {}

        for i, x_offset in enumerate(x_offsets_list):
            print(f"\n计算旋转中心X偏移 {x_offset} mm 的效果...")

            # 创建临时仿真对象
            temp_sim = HexagonFilmSimulation(rotation_center_x_offset=x_offset)

            # 运行短距离仿真 (360度) 用于对比
            temp_sim.total_rotation = 360
            temp_sim.theta_deg = np.arange(0, 361, 2)  # 简化计算
            temp_sim.theta = np.deg2rad(temp_sim.theta_deg)
            temp_sim.L = np.zeros(len(temp_sim.theta))
            temp_sim.S = np.zeros(len(temp_sim.theta))
            temp_sim.S_total = np.zeros(len(temp_sim.theta))  # 添加总薄膜长度数组
            temp_sim.contact_points = np.zeros((len(temp_sim.theta), 2))
            temp_sim.contact_type = [""] * len(temp_sim.theta)

            # 添加过辊接触点相关数组
            temp_sim.roller_contact_points = np.zeros((len(temp_sim.theta), 2))
            temp_sim.roller_contact_distances = np.zeros(len(temp_sim.theta))
            temp_sim.upper_point_trajectory = np.zeros((len(temp_sim.theta), 2))
            temp_sim.lower_point_trajectory = np.zeros((len(temp_sim.theta), 2))
            temp_sim.layer_numbers = np.zeros(len(temp_sim.theta), dtype=int)
            temp_sim.accumulated_thickness = np.zeros(len(temp_sim.theta))
            temp_sim.layer_consumption = np.zeros(len(temp_sim.theta))

            # 运行仿真
            temp_sim.run_simulation()
            results[f"x_offset_{x_offset}"] = temp_sim

            # 绘制接触轨迹
            ax1 = axes[0, i]
            ax1.set_aspect("equal")
            ax1.grid(True, alpha=0.3)
            ax1.set_title(
                f"旋转中心X偏移: {x_offset} mm\n接触轨迹",
                fontsize=12,
                fontweight="bold",
            )

            # 绘制过辊圆心和圆
            ax1.plot(
                temp_sim.A[0],
                temp_sim.A[1],
                "bo",
                markersize=12,
                markerfacecolor="blue",
                label="过辊",
            )

            # 绘制过辊圆
            roller_circle = plt.Circle(
                (temp_sim.A[0], temp_sim.A[1]),
                temp_sim.roller_radius,
                fill=False,
                color="blue",
                linewidth=1,
                alpha=0.5,
            )
            ax1.add_patch(roller_circle)

            # 绘制旋转中心
            ax1.plot(
                temp_sim.geometric_center[0],
                temp_sim.geometric_center[1],
                "gs",
                markersize=10,
                markerfacecolor="green",
                label="几何中心",
            )
            ax1.plot(
                temp_sim.rotation_center[0],
                temp_sim.rotation_center[1],
                "r^",
                markersize=10,
                markerfacecolor="red",
                label="旋转中心",
            )

            # 绘制偏移线
            ax1.plot(
                [temp_sim.geometric_center[0], temp_sim.rotation_center[0]],
                [temp_sim.geometric_center[1], temp_sim.rotation_center[1]],
                "k--",
                alpha=0.7,
                linewidth=2,
            )

            # 绘制初始六边形
            hex_x = np.append(temp_sim.vertices[:, 0], temp_sim.vertices[0, 0])
            hex_y = np.append(temp_sim.vertices[:, 1], temp_sim.vertices[0, 1])
            ax1.plot(hex_x, hex_y, "k-", linewidth=2, alpha=0.5)

            # 绘制接触轨迹
            ax1.plot(
                temp_sim.contact_points[:, 0],
                temp_sim.contact_points[:, 1],
                "r-",
                linewidth=2,
                alpha=0.8,
                label="接触轨迹",
            )
            ax1.plot(
                temp_sim.contact_points[::20, 0],
                temp_sim.contact_points[::20, 1],
                "ro",
                markersize=3,
                alpha=0.6,
            )

            # 绘制过辊接触点轨迹
            ax1.plot(
                temp_sim.roller_contact_points[:, 0],
                temp_sim.roller_contact_points[:, 1],
                "g-",
                linewidth=1.5,
                alpha=0.6,
                label="过辊接触轨迹",
            )
            ax1.plot(
                temp_sim.roller_contact_points[::20, 0],
                temp_sim.roller_contact_points[::20, 1],
                "go",
                markersize=2,
                alpha=0.5,
            )

            ax1.set_xlim(-60, 60)
            ax1.set_ylim(-20, 90)
            ax1.legend(fontsize=8)

            # 绘制长度变化
            ax2 = axes[1, i]
            ax2.grid(True, alpha=0.3)
            ax2.set_title("薄膜长度变化", fontsize=12, fontweight="bold")
            ax2.plot(
                temp_sim.theta_deg, temp_sim.L, "b-", linewidth=2, label="过辊到接触点"
            )
            ax2.plot(
                temp_sim.theta_deg,
                temp_sim.S_total,
                "r-",
                linewidth=2,
                label="总薄膜长度",
            )
            ax2.plot(
                temp_sim.theta_deg,
                temp_sim.roller_contact_distances,
                "g--",
                linewidth=1,
                alpha=0.7,
                label="切线部分",
            )
            ax2.set_xlabel("旋转角度 (度)")
            ax2.set_ylabel("长度")
            ax2.legend(fontsize=8)

            # 添加统计信息
            max_S_total = np.max(temp_sim.S_total)
            min_S_total = np.min(temp_sim.S_total)
            variation = max_S_total - min_S_total
            avg_tangent = np.mean(temp_sim.roller_contact_distances)
            ax2.text(
                0.02,
                0.98,
                f"总长度变化: {variation:.2f}\n最大: {max_S_total:.2f}\n最小: {min_S_total:.2f}\n平均切线: {avg_tangent:.2f}",
                transform=ax2.transAxes,
                verticalalignment="top",
                fontsize=8,
                bbox=dict(boxstyle="round", facecolor="wheat", alpha=0.8),
            )

        plt.tight_layout()
        plt.show()

        # 打印对比结果
        print("\n=== 旋转中心X偏移对比结果 ===")
        for x_offset in x_offsets_list:
            temp_sim = results[f"x_offset_{x_offset}"]
            max_S_total = np.max(temp_sim.S_total)
            min_S_total = np.min(temp_sim.S_total)
            variation = max_S_total - min_S_total
            total_S_total = temp_sim.S_total[-1]
            avg_tangent = np.mean(temp_sim.roller_contact_distances)
            total_surface = temp_sim.S[-1]
            print(f"X偏移 {x_offset} mm:")
            print(
                f"  总薄膜长度变化范围: {variation:.2f} (最大: {max_S_total:.2f}, 最小: {min_S_total:.2f})"
            )
            print(
                f"  最终总薄膜长度: {total_S_total:.2f} (切线: {avg_tangent:.2f}, 包覆: {total_surface:.2f})"
            )

        return results

    def create_static_plots(self, save_gif):
        """创建静态结果图（包含厚度分析和包覆长度）"""
        fig = plt.figure(figsize=(18, 12))

        # 图1：整体运动轨迹 (占据左侧两行)
        ax1 = plt.subplot(2, 3, (1, 4))
        ax1.set_aspect("equal")
        ax1.grid(True, alpha=0.3)
        ax1.set_title(
            f"六边形卷针旋转及薄膜包覆路径\n旋转中心X偏移: {self.rotation_center_x_offset:.1f} mm",
            fontsize=14,
            fontweight="bold",
        )
        ax1.set_xlabel("X轴")
        ax1.set_ylabel("Y轴")

        # 绘制固定过辊圆心和圆
        ax1.plot(
            self.A[0],
            self.A[1],
            "bo",
            markersize=12,
            markerfacecolor="blue",
            markeredgecolor="darkblue",
            markeredgewidth=2,
        )
        ax1.text(
            self.A[0] + 2,
            self.A[1],
            "过辊",
            fontsize=12,
            color="blue",
            fontweight="bold",
        )

        # 绘制过辊圆
        roller_circle = plt.Circle(
            (self.A[0], self.A[1]),
            self.roller_radius,
            fill=False,
            color="blue",
            linewidth=1.5,
            alpha=0.7,
        )
        ax1.add_patch(roller_circle)

        # 绘制几何中心和旋转中心
        ax1.plot(
            self.geometric_center[0],
            self.geometric_center[1],
            "gs",
            markersize=10,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            label="几何中心",
        )
        ax1.plot(
            self.rotation_center[0],
            self.rotation_center[1],
            "r^",
            markersize=10,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            label="旋转中心",
        )

        # 绘制偏移线
        ax1.plot(
            [self.geometric_center[0], self.rotation_center[0]],
            [self.geometric_center[1], self.rotation_center[1]],
            "k--",
            alpha=0.7,
            linewidth=2,
            label=f"X偏移: {self.rotation_center_x_offset:.1f}mm",
        )

        # 绘制初始六边形
        hex_patch = Polygon(
            self.vertices, fill=False, edgecolor="darkblue", linewidth=2.5, alpha=0.8
        )
        ax1.add_patch(hex_patch)

        # 绘制几个关键位置的六边形
        angles_to_show = [0, 60, 120, 180, 240, 300]  # 度
        colors = ["blue", "green", "orange", "red", "purple", "brown"]

        for angle, color in zip(angles_to_show, colors):
            idx = int(angle / self.step_angle)
            if idx < len(self.theta):
                rotated_vertices = self.rotate_vertices_around_center(self.theta[idx])

                hex_patch = Polygon(
                    rotated_vertices,
                    fill=False,
                    edgecolor=color,
                    linewidth=1.5,
                    alpha=0.6,
                    linestyle="--",
                )
                ax1.add_patch(hex_patch)

                # 绘制薄膜路径（通过过辊接触点）
                roller_contact = self.roller_contact_points[idx]
                contact = self.contact_points[idx]

                # 从接触点到过辊接触点的切线
                ax1.plot(
                    [contact[0], roller_contact[0]],
                    [contact[1], roller_contact[1]],
                    color=color,
                    linewidth=1.5,
                    alpha=0.7,
                    linestyle="--",
                )

                # 标记过辊接触点
                ax1.plot(
                    roller_contact[0],
                    roller_contact[1],
                    "o",
                    color=color,
                    markersize=4,
                    alpha=0.8,
                )

        # 绘制接触点轨迹
        ax1.plot(
            self.contact_points[::50, 0],
            self.contact_points[::50, 1],
            "r.",
            markersize=2,
            alpha=0.5,
            label="接触点轨迹",
        )

        # 绘制特殊点位轨迹
        ax1.plot(
            self.upper_point_trajectory[::50, 0],
            self.upper_point_trajectory[::50, 1],
            "m.",
            markersize=2,
            alpha=0.7,
            label="上方点轨迹",
        )
        ax1.plot(
            self.lower_point_trajectory[::50, 0],
            self.lower_point_trajectory[::50, 1],
            "c.",
            markersize=2,
            alpha=0.7,
            label="下方点轨迹",
        )

        # 绘制初始状态的A点到上方点连线
        ax1.plot(
            [self.A[0], self.upper_point[0]],
            [self.A[1], self.upper_point[1]],
            "m-",
            linewidth=3,
            alpha=0.8,
            label="初始连接：A-上方点",
        )

        # 标注特殊点位的初始和最终位置
        ax1.plot(
            self.upper_point[0],
            self.upper_point[1],
            "mo",
            markersize=10,
            markerfacecolor="magenta",
            markeredgecolor="darkmagenta",
            markeredgewidth=2,
        )
        ax1.text(
            self.upper_point[0] + 2,
            self.upper_point[1],
            "上方点初始",
            fontsize=10,
            color="magenta",
            fontweight="bold",
        )

        ax1.plot(
            self.lower_point[0],
            self.lower_point[1],
            "co",
            markersize=10,
            markerfacecolor="cyan",
            markeredgecolor="darkcyan",
            markeredgewidth=2,
        )
        ax1.text(
            self.lower_point[0] + 2,
            self.lower_point[1],
            "下方点初始",
            fontsize=10,
            color="cyan",
            fontweight="bold",
        )

        ax1.set_xlim(-60, 60)
        ax1.set_ylim(-20, 100)
        ax1.legend()

        # 图2：长度变化曲线
        ax2 = plt.subplot(2, 3, 2)
        ax2.grid(True, alpha=0.3)
        ax2.set_title("过辊到卷针长度变化", fontsize=12, fontweight="bold")
        ax2.plot(self.theta_deg, self.L, "b-", linewidth=2, label="薄膜长度 L(θ)")
        ax2.set_xlabel("旋转角度 (度)")
        ax2.set_ylabel("薄膜长度 L(θ)")
        ax2.set_xlim(0, self.total_rotation)
        ax2.legend()

        # 添加统计信息
        max_L = np.max(self.L)
        min_L = np.min(self.L)
        mean_L = np.mean(self.L)
        ax2.text(
            0.02,
            0.98,
            f"最大长度: {max_L:.2f}\n最小长度: {min_L:.2f}\n平均长度: {mean_L:.2f}",
            transform=ax2.transAxes,
            verticalalignment="top",
            fontsize=10,
            bbox=dict(boxstyle="round", facecolor="wheat", alpha=0.8),
        )

        # 图3：薄膜长度变化（切线+包覆）
        ax3 = plt.subplot(2, 3, 3)
        ax3.grid(True, alpha=0.3)
        ax3.set_title(
            "薄膜总长度变化\n(过辊切点+卷针包覆)", fontsize=12, fontweight="bold"
        )

        # 绘制总薄膜长度和各组成部分
        ax3.plot(self.theta_deg, self.S_total, "r-", linewidth=2, label="总薄膜长度")
        ax3.plot(
            self.theta_deg,
            self.roller_contact_distances,
            "g--",
            linewidth=1.5,
            alpha=0.7,
            label="切线部分",
        )
        ax3.plot(
            self.theta_deg,
            self.S,
            "b--",
            linewidth=1.5,
            alpha=0.7,
            label="卷针包覆部分",
        )

        ax3.set_xlabel("旋转角度 (度)")
        ax3.set_ylabel("薄膜长度")
        ax3.set_xlim(0, self.total_rotation)

        # 添加总长度标注
        total_S_total = self.S_total[-1]
        total_tangent = self.roller_contact_distances[-1]
        total_surface = self.S[-1]

        ax3.text(
            self.total_rotation / 2,
            total_S_total * 0.9,
            f"总长度: {total_S_total:.2f}\n切线: {total_tangent:.2f}\n包覆: {total_surface:.2f}",
            fontsize=9,
            ha="center",
            fontweight="bold",
            bbox=dict(boxstyle="round", facecolor="lightcoral", alpha=0.8),
        )
        ax3.legend()

        # 图4：隔膜厚度分析
        ax4 = plt.subplot(2, 3, 5)
        ax4.grid(True, alpha=0.3)
        ax4.set_title("隔膜厚度与层数分析", fontsize=12, fontweight="bold")

        # 左Y轴：累积厚度
        ax4_twin = ax4.twinx()

        # 绘制累积厚度
        line1 = ax4.plot(
            self.theta_deg,
            self.accumulated_thickness,
            "b-",
            linewidth=2,
            label=f"累积厚度 (厚度={self.film_thickness:.2f}mm/层)",
        )
        ax4.set_xlabel("旋转角度 (度)")
        ax4.set_ylabel("累积厚度 (mm)", color="b")
        ax4.tick_params(axis="y", labelcolor="b")
        ax4.set_xlim(0, self.total_rotation)

        # 右Y轴：层数
        line2 = ax4_twin.plot(
            self.theta_deg, self.layer_numbers, "r-", linewidth=2, label="层数"
        )
        ax4_twin.set_ylabel("层数", color="r")
        ax4_twin.tick_params(axis="y", labelcolor="r")

        # 合并图例
        lines = line1 + line2
        labels = [line.get_label() for line in lines]
        ax4.legend(lines, labels, loc="upper left")

        # 添加统计信息
        max_layers = np.max(self.layer_numbers)
        max_thickness = np.max(self.accumulated_thickness)
        total_consumption = np.sum(self.layer_consumption)

        info_text = f"最大层数: {max_layers}\n最大厚度: {max_thickness:.2f}mm\n总消耗: {total_consumption:.1f}mm"
        ax4.text(
            0.02,
            0.98,
            info_text,
            transform=ax4.transAxes,
            verticalalignment="top",
            fontsize=10,
            bbox=dict(boxstyle="round", facecolor="lightblue", alpha=0.8),
        )

        # 图5：隔膜消耗长度分析
        ax5 = plt.subplot(2, 3, 6)
        ax5.grid(True, alpha=0.3)
        ax5.set_title("每层隔膜消耗长度", fontsize=12, fontweight="bold")

        # 只显示有消耗的层数
        non_zero_indices = self.layer_consumption > 0
        if np.any(non_zero_indices):
            ax5.plot(
                self.theta_deg[non_zero_indices],
                self.layer_consumption[non_zero_indices],
                "g-",
                linewidth=2,
                label="每层消耗长度",
            )
            ax5.set_xlabel("旋转角度 (度)")
            ax5.set_ylabel("消耗长度 (mm)")
            ax5.set_xlim(0, self.total_rotation)
            ax5.legend()

            # 添加总消耗标注
            ax5.text(
                0.02,
                0.98,
                f"总消耗: {total_consumption:.1f}mm",
                transform=ax5.transAxes,
                verticalalignment="top",
                fontsize=10,
                bbox=dict(boxstyle="round", facecolor="lightgreen", alpha=0.8),
            )

        plt.tight_layout()
        plt.show()

        if save_gif:
            print("保存结果图片为PNG文件...")
            fig.savefig("hexagon_simulation.png")
            print("PNG保存完成!")

    def create_animation(self, save_gif=False):
        """创建动画"""
        print("创建动画...")

        fig, ax = plt.subplots(figsize=(12, 14))
        ax.set_aspect("equal")
        ax.grid(True, alpha=0.3)
        ax.set_title(
            f"六边形卷针薄膜包覆动态仿真\n旋转中心X偏移: {self.rotation_center_x_offset:.1f} mm",
            fontsize=16,
            fontweight="bold",
        )
        ax.set_xlabel("X轴", fontsize=12)
        ax.set_ylabel("Y轴", fontsize=12)
        ax.set_xlim(
            -(abs(self.vertices[:, 0]).max() + 10),
            (abs(self.vertices[:, 0]).max() + 10),
        )
        ax.set_ylim(-(abs(self.vertices[:, 0]).max() + 10), self.A[1] + 10)

        # 绘制固定过辊圆心和圆
        ax.plot(
            self.A[0],
            self.A[1],
            "bo",
            markersize=15,
            markerfacecolor="blue",
            markeredgecolor="darkblue",
            markeredgewidth=2,
            zorder=10,
        )
        ax.text(
            self.A[0] + 3,
            self.A[1],
            "过辊",
            fontsize=14,
            color="blue",
            fontweight="bold",
        )

        # 绘制过辊圆
        roller_circle = plt.Circle(
            (self.A[0], self.A[1]),
            self.roller_radius,
            fill=False,
            color="blue",
            linewidth=2,
            alpha=0.7,
            zorder=5,
        )
        ax.add_patch(roller_circle)

        # 绘制几何中心和旋转中心
        ax.plot(
            self.geometric_center[0],
            self.geometric_center[1],
            "gs",
            markersize=12,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            zorder=10,
            label="几何中心",
        )
        ax.plot(
            self.rotation_center[0],
            self.rotation_center[1],
            "r^",
            markersize=12,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            zorder=10,
            label="旋转中心",
        )

        # 绘制偏移线
        ax.plot(
            [self.geometric_center[0], self.rotation_center[0]],
            [self.geometric_center[1], self.rotation_center[1]],
            "k--",
            alpha=0.7,
            linewidth=2,
            zorder=5,
        )

        # 初始化动画元素
        (hex_line,) = ax.plot([], [], "b-", linewidth=3, label="六边形卷针")
        (film_line,) = ax.plot([], [], "r-", linewidth=3, label="薄膜切线")
        (contact_point,) = ax.plot(
            [],
            [],
            "ro",
            markersize=10,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            label="接触点",
        )

        # 过辊接触点
        (roller_contact_point,) = ax.plot(
            [],
            [],
            "go",
            markersize=8,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            label="过辊接触点",
        )

        # 特殊点位
        (upper_point,) = ax.plot(
            [],
            [],
            "mo",
            markersize=10,
            markerfacecolor="magenta",
            markeredgecolor="darkmagenta",
            markeredgewidth=2,
            label="上方点",
        )
        (lower_point,) = ax.plot(
            [],
            [],
            "co",
            markersize=10,
            markerfacecolor="cyan",
            markeredgecolor="darkcyan",
            markeredgewidth=2,
            label="下方点",
        )

        # A点到上方点的连线
        (connection_line,) = ax.plot(
            [], [], "m--", linewidth=2, alpha=0.7, label="A-上方点连线"
        )

        # 轨迹线
        (trajectory_line,) = ax.plot(
            [], [], "r.", markersize=1, alpha=0.3, label="接触轨迹"
        )
        (upper_trajectory,) = ax.plot(
            [], [], "m.", markersize=1, alpha=0.3, label="上方点轨迹"
        )
        (lower_trajectory,) = ax.plot(
            [], [], "c.", markersize=1, alpha=0.3, label="下方点轨迹"
        )

        # 信息文本
        info_text = ax.text(
            0.02,
            0.98,
            "",
            transform=ax.transAxes,
            fontsize=12,
            verticalalignment="top",
            fontweight="bold",
            bbox=dict(boxstyle="round", facecolor="yellow", alpha=0.8),
        )

        ax.legend(loc="upper right")

        # 为了动画流畅，每10帧显示一次
        frame_skip = 10
        frames = range(0, len(self.theta), frame_skip)

        def animate(frame_idx):
            i = frame_idx * frame_skip
            if i >= len(self.theta):
                return (
                    hex_line,
                    film_line,
                    contact_point,
                    roller_contact_point,
                    upper_point,
                    lower_point,
                    connection_line,
                    trajectory_line,
                    upper_trajectory,
                    lower_trajectory,
                    info_text,
                )

            # 使用新的旋转方法
            rotated_vertices = self.rotate_vertices_around_center(self.theta[i])

            # 更新六边形
            hex_x = np.append(rotated_vertices[:, 0], rotated_vertices[0, 0])
            hex_y = np.append(rotated_vertices[:, 1], rotated_vertices[0, 1])
            hex_line.set_data(hex_x, hex_y)

            # 更新薄膜切线 - 从接触点到过辊接触点
            contact = self.contact_points[i]
            roller_contact = self.roller_contact_points[i]

            # 绘制从接触点到过辊接触点的切线
            film_line.set_data(
                [contact[0], roller_contact[0]], [contact[1], roller_contact[1]]
            )
            film_line.set_color("green")
            film_line.set_linewidth(3)

            # 更新接触点
            contact_point.set_data([contact[0]], [contact[1]])

            # 更新过辊接触点
            roller_contact_point.set_data([roller_contact[0]], [roller_contact[1]])

            # 更新特殊点位
            upper_point.set_data(
                [self.upper_point_trajectory[i, 0]], [self.upper_point_trajectory[i, 1]]
            )
            lower_point.set_data(
                [self.lower_point_trajectory[i, 0]], [self.lower_point_trajectory[i, 1]]
            )

            # 更新A点到上方点的连线
            connection_line.set_data(
                [self.A[0], self.upper_point_trajectory[i, 0]],
                [self.A[1], self.upper_point_trajectory[i, 1]],
            )

            # 更新轨迹
            if i > 0:
                trajectory_line.set_data(
                    self.contact_points[:i:5, 0], self.contact_points[:i:5, 1]
                )
                upper_trajectory.set_data(
                    self.upper_point_trajectory[:i:5, 0],
                    self.upper_point_trajectory[:i:5, 1],
                )
                lower_trajectory.set_data(
                    self.lower_point_trajectory[:i:5, 0],
                    self.lower_point_trajectory[:i:5, 1],
                )

            # 更新信息文本
            angle_deg = self.theta_deg[i]
            current_L = self.L[i]
            current_S = self.S[i]
            current_S_total = self.S_total[i]
            contact_type = self.contact_type[i]
            upper_pos = self.upper_point_trajectory[i]
            lower_pos = self.lower_point_trajectory[i]
            tangent_length = self.roller_contact_distances[i]

            info_text.set_text(
                f"角度: {angle_deg:.1f}°\n"
                f"过辊到接触点: {current_L:.2f}\n"
                f"总薄膜长度: {current_S_total:.2f}\n"
                f"  切线部分: {tangent_length:.2f}\n"
                f"  包覆部分: {current_S:.2f}\n"
                f"接触类型: {contact_type}\n"
                f"上方点: ({upper_pos[0]:.1f}, {upper_pos[1]:.1f})\n"
                f"下方点: ({lower_pos[0]:.1f}, {lower_pos[1]:.1f})\n"
                f"X偏移: {self.rotation_center_x_offset:.1f} mm"
            )

            return (
                hex_line,
                film_line,
                contact_point,
                roller_contact_point,
                upper_point,
                lower_point,
                connection_line,
                trajectory_line,
                upper_trajectory,
                lower_trajectory,
                info_text,
            )

        # 创建动画
        anim = animation.FuncAnimation(
            fig, animate, frames=len(frames), interval=50, blit=True, repeat=True
        )

        if save_gif:
            print("保存动画为GIF文件...")
            anim.save("hexagon_film_simulation.gif", writer="pillow", fps=20)
            print("GIF保存完成!")

        plt.show()
        return anim


def main():
    """主函数 - 简化版"""
    print("=" * 60)
    print("非正六边形卷针薄膜包覆动态仿真")
    print("=" * 60)

    # 创建仿真对象 (默认X偏移=2.0mm)
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0)

    print("\n🎯 仿真参数:")
    print(f"过辊位置A: ({sim.A[0]:.1f}, {sim.A[1]:.1f})")
    print(f"几何中心: ({sim.geometric_center[0]:.1f}, {sim.geometric_center[1]:.1f})")
    print(f"旋转中心X偏移: {sim.rotation_center_x_offset:.1f} mm")
    print(f"旋转中心位置: ({sim.rotation_center[0]:.1f}, {sim.rotation_center[1]:.1f})")
    print(f"上方特殊点位: ({sim.upper_point[0]:.1f}, {sim.upper_point[1]:.1f})")
    print(f"下方特殊点位: ({sim.lower_point[0]:.1f}, {sim.lower_point[1]:.1f})")
    print(f"初始连接：A点到上方点距离: {sim.initial_connection_length:.2f}")

    # 可选：修改旋转中心偏移
    choice = input("\n是否修改旋转中心X偏移? (y/n): ").lower()
    if choice == "y":
        new_offset = float(
            input(f"输入新的X偏移 (当前: {sim.rotation_center_x_offset:.1f} mm): ")
        )
        sim.set_rotation_center_x_offset(new_offset)

    # 可选：查看接触点分析
    choice = input("\n是否查看接触点分析? (y/n): ").lower()
    if choice == "y":
        test_angles = [0, 60, 120, 180]
        for angle in test_angles:
            sim.visualize_contact_analysis(angle)
            if angle < test_angles[-1]:  # 不是最后一个
                input("按回车继续下一个角度...")

    # # 可选：对比不同X偏移
    # choice = input("\n是否对比不同X偏移的效果? (y/n): ").lower()
    # if choice == "y":
    #     x_offsets = [0.0, 1.0, 2.0, 3.0, 4.0]  # 5个不同偏移
    #     print(f"对比X偏移: {x_offsets}")
    #     sim.compare_rotation_centers(x_offsets)

    # 运行完整仿真
    print("\n🚀 运行完整仿真...")
    sim.run_simulation()

    # 显示结果统计
    print("\n📊 仿真结果:")
    print(
        f"过辊到接触点长度 - 最大: {np.max(sim.L):.2f}, 最小: {np.min(sim.L):.2f}, 变化: {np.max(sim.L) - np.min(sim.L):.2f}"
    )
    print(f"总薄膜长度: {sim.S_total[-1]:.2f}")
    print(f"  其中切线部分: {sim.roller_contact_distances[-1]:.2f}")
    print(f"  其中包覆部分: {sim.S[-1]:.2f}")

    save_gif = input("是否保存为PNG? (y/n): ").lower() == "y"

    # 创建静态图
    sim.create_static_plots(save_gif=save_gif)

    # 询问是否创建动画
    choice = input("\n🎬 是否创建动画? (y/n): ").lower()
    if choice == "y":
        save_gif = input("是否保存为GIF? (y/n): ").lower() == "y"
        _ = sim.create_animation(save_gif=save_gif)


if __name__ == "__main__":
    main()
