#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双接触点系统
过辊A有两个接触点：
1. AB切线接触点：固定在(0.5, 82)
2. 动态切点：从卷针接触点到过辊A的切线接触点
"""

import numpy as np
import matplotlib.pyplot as plt
from main import HexagonFilmSimulation

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False

def test_dual_contact_points():
    """测试双接触点系统"""
    print("=== 测试过辊A的双接触点系统 ===")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0, film_thickness=0.1)
    
    print(f"过辊A位置: ({sim.A[0]:.1f}, {sim.A[1]:.1f})")
    print(f"过辊B位置: ({sim.B[0]:.1f}, {sim.B[1]:.1f})")
    print(f"过辊半径: {sim.roller_radius:.1f} mm")
    
    # 测试几个关键角度
    test_angles = [0, 45, 90, 135, 180, 225, 270, 315, 360]
    
    print("\n=== 各角度的双接触点分析 ===")
    print("角度    卷针接触点           AB切线点        动态切点        圆弧A长度")
    print("-" * 80)
    
    for angle in test_angles:
        # 计算层数和累积厚度
        layer_num, accum_thickness = sim.calculate_layer_from_angle(angle)
        
        # 更新顶点位置
        if accum_thickness > 0:
            current_vertices = sim.update_vertices_with_thickness(accum_thickness)
            rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
                np.deg2rad(angle), current_vertices
            )
        else:
            rotated_vertices = sim.rotate_vertices_around_center(np.deg2rad(angle))
        
        # 旋转特殊点位
        rotated_upper, rotated_lower = sim.rotate_special_points_around_center(
            np.deg2rad(angle), accum_thickness
        )
        
        # 寻找卷针接触点
        contact, contact_type = sim.find_leftmost_valid_contact_point(
            sim.A, rotated_vertices, rotated_upper, angle
        )
        
        # 计算双过辊系统的各个长度分量
        tangent_AB, arc_A, arc_B, tangent_A, contact_A_dynamic, contact_A_entry, contact_B_exit = sim.calculate_roller_to_roller_distance(contact)
        
        print(f"{angle:3.0f}°   ({contact[0]:5.1f},{contact[1]:5.1f})   ({contact_A_entry[0]:4.1f},{contact_A_entry[1]:4.1f})   ({contact_A_dynamic[0]:5.1f},{contact_A_dynamic[1]:5.1f})   {arc_A:6.2f}mm")

def run_dual_contact_simulation():
    """运行双接触点完整仿真"""
    print("\n=== 运行双接触点完整仿真 ===")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0, film_thickness=0.1)
    
    # 设置较短的仿真（一圈）
    sim.total_rotation = 360
    sim.step_angle = 10.0  # 较大步长以便观察
    sim.theta_deg = np.arange(0, sim.total_rotation + sim.step_angle, sim.step_angle)
    sim.theta = np.deg2rad(sim.theta_deg)
    
    # 重新初始化数组
    sim.L = np.zeros(len(sim.theta))
    sim.S = np.zeros(len(sim.theta))
    sim.S_total = np.zeros(len(sim.theta))
    sim.contact_points = np.zeros((len(sim.theta), 2))
    sim.contact_type = [""] * len(sim.theta)
    sim.roller_contact_points = np.zeros((len(sim.theta), 2))
    sim.roller_contact_distances = np.zeros(len(sim.theta))
    sim.upper_point_trajectory = np.zeros((len(sim.theta), 2))
    sim.lower_point_trajectory = np.zeros((len(sim.theta), 2))
    sim.layer_numbers = np.zeros(len(sim.theta), dtype=int)
    sim.accumulated_thickness = np.zeros(len(sim.theta))
    sim.layer_consumption = np.zeros(len(sim.theta))
    
    # 双过辊数组
    sim.tangent_A_lengths = np.zeros(len(sim.theta))
    sim.tangent_AB_lengths = np.zeros(len(sim.theta))
    sim.arc_A_lengths = np.zeros(len(sim.theta))
    sim.arc_B_lengths = np.zeros(len(sim.theta))
    sim.contact_A_points = np.zeros((len(sim.theta), 2))
    sim.contact_B_points = np.zeros((len(sim.theta), 2))
    sim.contact_A_entry_points = np.zeros((len(sim.theta), 2))
    
    print("开始仿真...")
    sim.run_simulation()
    print("仿真完成!")
    
    # 分析结果
    print(f"\n=== 双接触点仿真结果分析 ===")
    print(f"总包覆长度: {sim.S[-1]:.2f} mm")
    print(f"平均切线A长度: {np.mean(sim.tangent_A_lengths):.2f} mm")
    print(f"平均切线AB长度: {np.mean(sim.tangent_AB_lengths):.2f} mm")
    print(f"平均圆弧A长度: {np.mean(sim.arc_A_lengths):.2f} mm")
    print(f"平均圆弧B长度: {np.mean(sim.arc_B_lengths):.2f} mm")
    print(f"最终总薄膜长度: {sim.S_total[-1]:.2f} mm")
    
    # 验证AB切线接触点是否恒定
    entry_point_std_x = np.std(sim.contact_A_entry_points[:, 0])
    entry_point_std_y = np.std(sim.contact_A_entry_points[:, 1])
    print(f"AB切线接触点稳定性: X标准差={entry_point_std_x:.6f}, Y标准差={entry_point_std_y:.6f}")
    
    # 分析动态切点的变化范围
    dynamic_x_range = np.max(sim.contact_A_points[:, 0]) - np.min(sim.contact_A_points[:, 0])
    dynamic_y_range = np.max(sim.contact_A_points[:, 1]) - np.min(sim.contact_A_points[:, 1])
    print(f"动态切点变化范围: X={dynamic_x_range:.2f}mm, Y={dynamic_y_range:.2f}mm")
    
    return sim

def plot_dual_contact_analysis(sim):
    """绘制双接触点分析图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 图1：过辊A的两个接触点轨迹
    ax1.plot(sim.contact_A_entry_points[:, 0], sim.contact_A_entry_points[:, 1], 
             'bo-', markersize=4, linewidth=2, label='AB切线接触点（固定）')
    ax1.plot(sim.contact_A_points[:, 0], sim.contact_A_points[:, 1], 
             'ro-', markersize=3, linewidth=1, label='动态切点（变化）')
    
    # 绘制过辊A
    circle_A = plt.Circle(sim.A, sim.roller_radius, fill=False, color='green', linewidth=2)
    ax1.add_patch(circle_A)
    ax1.plot(sim.A[0], sim.A[1], 'go', markersize=8, label='过辊A中心')
    
    ax1.grid(True, alpha=0.3)
    ax1.set_title('过辊A的双接触点轨迹', fontsize=14, fontweight='bold')
    ax1.set_xlabel('X坐标 (mm)')
    ax1.set_ylabel('Y坐标 (mm)')
    ax1.legend()
    ax1.set_aspect('equal')
    
    # 图2：圆弧A长度变化（AB切线点到动态切点的圆弧）
    ax2.plot(sim.theta_deg, sim.arc_A_lengths, 'g-', linewidth=2, label='圆弧A长度')
    ax2.grid(True, alpha=0.3)
    ax2.set_title('过辊A圆弧长度变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('旋转角度 (度)')
    ax2.set_ylabel('圆弧长度 (mm)')
    ax2.legend()
    
    # 图3：各长度分量对比
    ax3.plot(sim.theta_deg, sim.tangent_A_lengths, 'b-', linewidth=2, label='切线A长度')
    ax3.plot(sim.theta_deg, sim.tangent_AB_lengths, 'g-', linewidth=2, label='切线AB长度')
    ax3.plot(sim.theta_deg, sim.arc_A_lengths, 'm-', linewidth=2, label='圆弧A长度')
    ax3.plot(sim.theta_deg, sim.arc_B_lengths, 'c-', linewidth=2, label='圆弧B长度')
    ax3.grid(True, alpha=0.3)
    ax3.set_title('各长度分量对比', fontsize=14, fontweight='bold')
    ax3.set_xlabel('旋转角度 (度)')
    ax3.set_ylabel('长度 (mm)')
    ax3.legend()
    
    # 图4：系统布局图
    # 绘制过辊圆
    circle_A = plt.Circle(sim.A, sim.roller_radius, fill=False, color='blue', linewidth=2, label='过辊A')
    circle_B = plt.Circle(sim.B, sim.roller_radius, fill=False, color='red', linewidth=2, label='过辊B')
    ax4.add_patch(circle_A)
    ax4.add_patch(circle_B)
    
    # 绘制六边形（初始位置）
    hex_patch = plt.Polygon(sim.vertices, fill=False, edgecolor='green', linewidth=2, label='六边形卷针')
    ax4.add_patch(hex_patch)
    
    # 标注关键点
    ax4.plot(sim.A[0], sim.A[1], 'bo', markersize=8, label='过辊A中心')
    ax4.plot(sim.B[0], sim.B[1], 'ro', markersize=8, label='过辊B中心')
    
    # 绘制薄膜走带路径和两个接触点
    B_exit = np.array([-30.0, 82.0])
    A_entry = np.array([0.5, 82.0])
    ax4.plot([B_exit[0], A_entry[0]], [B_exit[1], A_entry[1]], 'k-', linewidth=3, alpha=0.7, label='薄膜走带')
    ax4.plot(B_exit[0], B_exit[1], 'ko', markersize=6, label='过辊B出线点')
    ax4.plot(A_entry[0], A_entry[1], 'ko', markersize=6, label='AB切线接触点')
    
    # 显示一个示例动态切点
    if len(sim.contact_A_points) > 0:
        example_dynamic = sim.contact_A_points[0]
        ax4.plot(example_dynamic[0], example_dynamic[1], 'mo', markersize=6, label='动态切点（示例）')
    
    # 添加标注
    ax4.text(sim.A[0], sim.A[1]+5, f'A({sim.A[0]:.1f}, {sim.A[1]:.1f})', ha='center', fontsize=10)
    ax4.text(sim.B[0], sim.B[1]+5, f'B({sim.B[0]:.1f}, {sim.B[1]:.1f})', ha='center', fontsize=10)
    ax4.text(A_entry[0], A_entry[1]+3, f'AB切线点\n({A_entry[0]:.1f}, {A_entry[1]:.1f})', ha='center', fontsize=9)
    
    ax4.grid(True, alpha=0.3)
    ax4.set_title('双接触点系统布局', fontsize=14, fontweight='bold')
    ax4.set_xlabel('X坐标 (mm)')
    ax4.set_ylabel('Y坐标 (mm)')
    ax4.legend()
    ax4.set_aspect('equal')
    ax4.set_xlim(-40, 20)
    ax4.set_ylim(-30, 90)
    
    plt.tight_layout()
    plt.savefig('dual_contact_points_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print('图表已保存为 dual_contact_points_analysis.png')

if __name__ == "__main__":
    # 运行基础测试
    test_dual_contact_points()
    
    # 运行完整仿真
    sim = run_dual_contact_simulation()
    
    # 绘制分析图
    plot_dual_contact_analysis(sim)
    
    print("\n=== 双接触点系统总结 ===")
    print("✓ 过辊A有两个接触点：")
    print("  1. AB切线接触点：固定在(0.5, 82)")
    print("  2. 动态切点：随卷针旋转变化")
    print("✓ 圆弧A长度：从AB切线点到动态切点的圆弧")
    print("✓ 薄膜路径：过辊B → AB切线点 → 动态切点 → 卷针接触点")
