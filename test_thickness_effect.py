#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试厚度影响对接触点位置和包覆长度计算的影响
"""

import numpy as np
import matplotlib.pyplot as plt
from main import HexagonFilmSimulation

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False

def test_thickness_effect():
    """测试厚度影响"""
    print("=== 测试厚度影响对接触点和包覆长度的影响 ===")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0, film_thickness=0.1)
    
    # 测试几个关键角度
    test_angles = [0, 90, 180, 270, 360, 450]  # 包括第二圈的角度
    
    print("原始顶点坐标:")
    for i, vertex in enumerate(sim.original_vertices):
        print(f"  V{i+1}: ({vertex[0]:6.1f}, {vertex[1]:6.1f})")
    
    print(f"\n原始特殊点位:")
    print(f"  上方点(2,4): ({sim.upper_point[0]:6.1f}, {sim.upper_point[1]:6.1f})")
    print(f"  下方点(-2,-4): ({sim.lower_point[0]:6.1f}, {sim.lower_point[1]:6.1f})")
    
    results = []
    
    for angle in test_angles:
        print(f"\n--- 角度 {angle}° ---")
        
        # 计算层数和累积厚度
        layer_num, accum_thickness = sim.calculate_layer_from_angle(angle)
        print(f"层数: {layer_num}, 累积厚度: {accum_thickness:.3f} mm")
        
        # 更新顶点位置（考虑厚度）
        if accum_thickness > 0:
            current_vertices = sim.update_vertices_with_thickness(accum_thickness)
            print("考虑厚度影响后的顶点坐标:")
            for i, vertex in enumerate(current_vertices):
                original = sim.original_vertices[i]
                delta = vertex - original
                print(f"  V{i+1}: ({vertex[0]:6.1f}, {vertex[1]:6.1f}) [Δ=({delta[0]:+5.2f}, {delta[1]:+5.2f})]")
        else:
            current_vertices = sim.original_vertices.copy()
            print("使用原始顶点坐标（无厚度影响）")
        
        # 旋转顶点
        theta = np.deg2rad(angle)
        if accum_thickness > 0:
            rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(theta, current_vertices)
        else:
            rotated_vertices = sim.rotate_vertices_around_center(theta)
        
        # 旋转特殊点位（考虑厚度）
        rotated_upper, rotated_lower = sim.rotate_special_points_around_center(theta, accum_thickness)
        
        print(f"旋转后的特殊点位:")
        print(f"  上方点: ({rotated_upper[0]:6.1f}, {rotated_upper[1]:6.1f})")
        print(f"  下方点: ({rotated_lower[0]:6.1f}, {rotated_lower[1]:6.1f})")
        
        # 寻找接触点
        contact, contact_type = sim.find_leftmost_valid_contact_point(
            sim.A, rotated_vertices, rotated_upper, angle
        )
        
        print(f"接触点: ({contact[0]:6.1f}, {contact[1]:6.1f}), 类型: {contact_type}")
        
        # 计算到过辊的距离
        distance_to_roller = np.linalg.norm(sim.A - contact)
        print(f"到过辊距离: {distance_to_roller:.2f} mm")
        
        # 计算过辊接触点
        roller_contact = sim.find_roller_contact_point(contact)
        tangent_length = np.linalg.norm(contact - roller_contact)
        print(f"过辊接触点: ({roller_contact[0]:6.1f}, {roller_contact[1]:6.1f})")
        print(f"切线长度: {tangent_length:.2f} mm")
        
        results.append({
            'angle': angle,
            'layer_num': layer_num,
            'accum_thickness': accum_thickness,
            'contact': contact,
            'contact_type': contact_type,
            'distance_to_roller': distance_to_roller,
            'tangent_length': tangent_length,
            'rotated_upper': rotated_upper,
            'current_vertices': current_vertices
        })
    
    return results

def compare_edge_lengths(sim):
    """比较不同厚度下的边长"""
    print("\n=== 边长对比分析 ===")
    
    # 原始边长
    original_edges = []
    for i in range(len(sim.original_vertices)):
        v1 = sim.original_vertices[i]
        v2 = sim.original_vertices[(i + 1) % len(sim.original_vertices)]
        edge_length = np.linalg.norm(v2 - v1)
        edge_name = f"V{i+1}-V{((i+1) % len(sim.original_vertices))+1}"
        original_edges.append((edge_name, edge_length))
        print(f"原始 {edge_name}: {edge_length:.3f} mm")
    
    # 考虑厚度后的边长
    print(f"\n考虑厚度 {sim.film_thickness:.1f} mm 后:")
    thick_vertices = sim.update_vertices_with_thickness(sim.film_thickness)
    thick_edges = []
    for i in range(len(thick_vertices)):
        v1 = thick_vertices[i]
        v2 = thick_vertices[(i + 1) % len(thick_vertices)]
        edge_length = np.linalg.norm(v2 - v1)
        edge_name = f"V{i+1}-V{((i+1) % len(thick_vertices))+1}"
        thick_edges.append((edge_name, edge_length))
        original_length = original_edges[i][1]
        delta = edge_length - original_length
        print(f"厚度后 {edge_name}: {edge_length:.3f} mm [Δ={delta:+6.3f}]")
    
    return original_edges, thick_edges

def plot_thickness_comparison(results):
    """绘制厚度影响对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    angles = [r['angle'] for r in results]
    distances = [r['distance_to_roller'] for r in results]
    tangent_lengths = [r['tangent_length'] for r in results]
    thicknesses = [r['accum_thickness'] for r in results]
    
    # 图1：到过辊距离变化
    ax1.plot(angles, distances, 'bo-', linewidth=2, markersize=8)
    ax1.grid(True, alpha=0.3)
    ax1.set_title('到过辊距离 vs 角度', fontsize=14, fontweight='bold')
    ax1.set_xlabel('角度 (度)')
    ax1.set_ylabel('距离 (mm)')
    
    # 标注厚度信息
    for i, (angle, dist, thick) in enumerate(zip(angles, distances, thicknesses)):
        ax1.annotate(f'{thick:.1f}mm', (angle, dist), 
                    textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
    
    # 图2：切线长度变化
    ax2.plot(angles, tangent_lengths, 'ro-', linewidth=2, markersize=8)
    ax2.grid(True, alpha=0.3)
    ax2.set_title('切线长度 vs 角度', fontsize=14, fontweight='bold')
    ax2.set_xlabel('角度 (度)')
    ax2.set_ylabel('切线长度 (mm)')
    
    # 图3：累积厚度
    ax3.plot(angles, thicknesses, 'go-', linewidth=2, markersize=8)
    ax3.grid(True, alpha=0.3)
    ax3.set_title('累积厚度 vs 角度', fontsize=14, fontweight='bold')
    ax3.set_xlabel('角度 (度)')
    ax3.set_ylabel('累积厚度 (mm)')
    
    # 图4：接触点类型
    contact_types = [r['contact_type'] for r in results]
    unique_types = list(set(contact_types))
    type_to_num = {t: i for i, t in enumerate(unique_types)}
    contact_nums = [type_to_num[t] for t in contact_types]
    
    ax4.plot(angles, contact_nums, 'mo-', linewidth=2, markersize=8)
    ax4.grid(True, alpha=0.3)
    ax4.set_title('接触点类型 vs 角度', fontsize=14, fontweight='bold')
    ax4.set_xlabel('角度 (度)')
    ax4.set_ylabel('接触点类型')
    ax4.set_yticks(range(len(unique_types)))
    ax4.set_yticklabels([t.replace('leftmost_', '').replace('_upper_point', '(2,4)') 
                        for t in unique_types], fontsize=8)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # 运行测试
    results = test_thickness_effect()
    
    # 创建仿真对象用于边长对比
    sim = HexagonFilmSimulation()
    original_edges, thick_edges = compare_edge_lengths(sim)
    
    # 绘制对比图
    plot_thickness_comparison(results)
    
    print("\n=== 测试总结 ===")
    print("✓ 厚度影响已正确考虑到接触点位置计算")
    print("✓ 特殊点位(2,4)和(-2,-4)也会随厚度调整")
    print("✓ 切线长度计算基于厚度影响后的接触点")
    print("✓ 第二圈时顶点坐标已考虑0.1mm厚度影响")
