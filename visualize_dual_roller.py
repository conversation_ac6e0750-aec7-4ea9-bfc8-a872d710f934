#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双过辊系统可视化
"""

import numpy as np
import matplotlib.pyplot as plt
from main import HexagonFilmSimulation

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_dual_roller_visualization():
    """创建双过辊系统可视化"""
    
    # 创建仿真对象并运行简短仿真
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0, film_thickness=0.1)
    sim.total_rotation = 360
    sim.step_angle = 5.0
    sim.theta_deg = np.arange(0, sim.total_rotation + sim.step_angle, sim.step_angle)
    sim.theta = np.deg2rad(sim.theta_deg)

    # 重新初始化数组
    sim.L = np.zeros(len(sim.theta))
    sim.S = np.zeros(len(sim.theta))
    sim.S_total = np.zeros(len(sim.theta))
    sim.contact_points = np.zeros((len(sim.theta), 2))
    sim.contact_type = [''] * len(sim.theta)
    sim.roller_contact_points = np.zeros((len(sim.theta), 2))
    sim.roller_contact_distances = np.zeros(len(sim.theta))
    sim.upper_point_trajectory = np.zeros((len(sim.theta), 2))
    sim.lower_point_trajectory = np.zeros((len(sim.theta), 2))
    sim.layer_numbers = np.zeros(len(sim.theta), dtype=int)
    sim.accumulated_thickness = np.zeros(len(sim.theta))
    sim.layer_consumption = np.zeros(len(sim.theta))
    sim.tangent_A_lengths = np.zeros(len(sim.theta))
    sim.tangent_AB_lengths = np.zeros(len(sim.theta))
    sim.arc_A_lengths = np.zeros(len(sim.theta))
    sim.arc_B_lengths = np.zeros(len(sim.theta))
    sim.contact_A_points = np.zeros((len(sim.theta), 2))
    sim.contact_B_points = np.zeros((len(sim.theta), 2))

    print("运行仿真...")
    sim.run_simulation()
    print("仿真完成!")

    # 创建可视化图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 图1：各长度分量变化
    ax1.plot(sim.theta_deg, sim.tangent_A_lengths, 'b-', linewidth=2, label='切线A长度')
    ax1.plot(sim.theta_deg, sim.S, 'r-', linewidth=2, label='包覆长度')
    ax1.plot(sim.theta_deg, sim.tangent_AB_lengths, 'g-', linewidth=2, label='切线AB长度')
    ax1.plot(sim.theta_deg, sim.arc_A_lengths, 'm-', linewidth=2, label='圆弧A长度')
    ax1.plot(sim.theta_deg, sim.arc_B_lengths, 'c-', linewidth=2, label='圆弧B长度')
    ax1.grid(True, alpha=0.3)
    ax1.set_title('各长度分量变化', fontsize=14, fontweight='bold')
    ax1.set_xlabel('旋转角度 (度)')
    ax1.set_ylabel('长度 (mm)')
    ax1.legend()

    # 图2：总长度变化
    ax2.plot(sim.theta_deg, sim.S_total, 'k-', linewidth=3, label='总薄膜长度')
    ax2.grid(True, alpha=0.3)
    ax2.set_title('总薄膜长度变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('旋转角度 (度)')
    ax2.set_ylabel('总长度 (mm)')
    ax2.legend()

    # 图3：长度分量堆叠图
    ax3.fill_between(sim.theta_deg, 0, sim.tangent_A_lengths, alpha=0.7, label='切线A')
    ax3.fill_between(sim.theta_deg, sim.tangent_A_lengths, 
                    sim.tangent_A_lengths + sim.S, alpha=0.7, label='包覆长度')
    ax3.fill_between(sim.theta_deg, sim.tangent_A_lengths + sim.S,
                    sim.tangent_A_lengths + sim.S + sim.tangent_AB_lengths, 
                    alpha=0.7, label='切线AB')
    ax3.fill_between(sim.theta_deg, sim.tangent_A_lengths + sim.S + sim.tangent_AB_lengths,
                    sim.tangent_A_lengths + sim.S + sim.tangent_AB_lengths + sim.arc_A_lengths,
                    alpha=0.7, label='圆弧A')
    ax3.fill_between(sim.theta_deg, sim.tangent_A_lengths + sim.S + sim.tangent_AB_lengths + sim.arc_A_lengths,
                    sim.S_total, alpha=0.7, label='圆弧B')
    ax3.grid(True, alpha=0.3)
    ax3.set_title('长度分量堆叠图', fontsize=14, fontweight='bold')
    ax3.set_xlabel('旋转角度 (度)')
    ax3.set_ylabel('累积长度 (mm)')
    ax3.legend()

    # 图4：系统布局图
    # 绘制过辊圆
    circle_A = plt.Circle(sim.A, sim.roller_radius, fill=False, color='blue', linewidth=2, label='过辊A')
    circle_B = plt.Circle(sim.B, sim.roller_radius, fill=False, color='red', linewidth=2, label='过辊B')
    ax4.add_patch(circle_A)
    ax4.add_patch(circle_B)

    # 绘制六边形（初始位置）
    hex_patch = plt.Polygon(sim.vertices, fill=False, edgecolor='green', linewidth=2, label='六边形卷针')
    ax4.add_patch(hex_patch)

    # 标注关键点
    ax4.plot(sim.A[0], sim.A[1], 'bo', markersize=8, label='过辊A中心')
    ax4.plot(sim.B[0], sim.B[1], 'ro', markersize=8, label='过辊B中心')
    ax4.plot(sim.rotation_center[0], sim.rotation_center[1], 'go', markersize=8, label='旋转中心')

    # 添加标注
    ax4.text(sim.A[0], sim.A[1]+5, f'A({sim.A[0]:.1f}, {sim.A[1]:.1f})', ha='center', fontsize=10)
    ax4.text(sim.B[0], sim.B[1]+5, f'B({sim.B[0]:.1f}, {sim.B[1]:.1f})', ha='center', fontsize=10)

    # 绘制AB连线
    ax4.plot([sim.A[0], sim.B[0]], [sim.A[1], sim.B[1]], 'k--', alpha=0.5, linewidth=1)
    
    # 计算并显示AB距离
    AB_distance = np.linalg.norm(sim.B - sim.A)
    mid_point = (sim.A + sim.B) / 2
    ax4.text(mid_point[0], mid_point[1]+3, f'距离: {AB_distance:.1f}mm', ha='center', fontsize=9)

    ax4.grid(True, alpha=0.3)
    ax4.set_title('双过辊系统布局', fontsize=14, fontweight='bold')
    ax4.set_xlabel('X坐标 (mm)')
    ax4.set_ylabel('Y坐标 (mm)')
    ax4.legend()
    ax4.set_aspect('equal')
    ax4.set_xlim(-40, 40)
    ax4.set_ylim(-30, 90)

    plt.tight_layout()
    plt.savefig('dual_roller_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    print('图表已保存为 dual_roller_analysis.png')
    
    # 打印详细统计信息
    print(f"\n=== 双过辊系统统计信息 ===")
    print(f"过辊A位置: ({sim.A[0]:.1f}, {sim.A[1]:.1f})")
    print(f"过辊B位置: ({sim.B[0]:.1f}, {sim.B[1]:.1f})")
    print(f"过辊间距: {AB_distance:.1f} mm")
    print(f"过辊半径: {sim.roller_radius:.1f} mm")
    
    print(f"\n=== 最终长度分量 ===")
    final_tangent_A = sim.tangent_A_lengths[-1]
    final_wrapping = sim.S[-1]
    final_tangent_AB = sim.tangent_AB_lengths[-1]
    final_arc_A = sim.arc_A_lengths[-1]
    final_arc_B = sim.arc_B_lengths[-1]
    final_total = sim.S_total[-1]
    
    print(f"1. 切线A长度: {final_tangent_A:6.2f} mm ({final_tangent_A/final_total*100:5.1f}%)")
    print(f"2. 包覆长度:   {final_wrapping:6.2f} mm ({final_wrapping/final_total*100:5.1f}%)")
    print(f"3. 切线AB长度: {final_tangent_AB:6.2f} mm ({final_tangent_AB/final_total*100:5.1f}%)")
    print(f"4. 圆弧A长度:  {final_arc_A:6.2f} mm ({final_arc_A/final_total*100:5.1f}%)")
    print(f"5. 圆弧B长度:  {final_arc_B:6.2f} mm ({final_arc_B/final_total*100:5.1f}%)")
    print(f"总计:         {final_total:6.2f} mm (100.0%)")
    
    return sim

if __name__ == "__main__":
    sim = create_dual_roller_visualization()
