{".class": "MypyFile", "_fullname": "main", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HexagonFilmSimulation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "main.HexagonFilmSimulation", "name": "HexagonFilmSimulation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "main", "mro": ["main.HexagonFilmSimulation", "builtins.object"], "names": {".class": "SymbolTable", "A": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.A", "name": "A", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "L": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.L", "name": "L", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "S": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.S", "name": "S", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "S_total": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.S_total", "name": "S_total", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "rotation_center_x_offset", "film_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.__init__", "name": "__init__", "type": null}}, "accumulated_thickness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.accumulated_thickness", "name": "accumulated_thickness", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "calc_tangent_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cx", "cy", "radius", "outside_point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.calc_tangent_points", "name": "calc_tangent_points", "type": null}}, "calculate_layer_from_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "angle_deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.calculate_layer_from_angle", "name": "calculate_layer_from_angle", "type": null}}, "calculate_wrapping_length_increment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "prev_contact_type", "current_contact_type", "prev_contact_point", "current_contact_point", "current_vertices", "angle_deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.calculate_wrapping_length_increment", "name": "calculate_wrapping_length_increment", "type": null}}, "compare_rotation_centers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x_offsets_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.compare_rotation_centers", "name": "compare_rotation_centers", "type": null}}, "contact_points": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.contact_points", "name": "contact_points", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "contact_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.contact_type", "name": "contact_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_animation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "save_gif"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.create_animation", "name": "create_animation", "type": null}}, "create_static_plots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "save_gif"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.create_static_plots", "name": "create_static_plots", "type": null}}, "current_thickness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.current_thickness", "name": "current_thickness", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "film_thickness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.film_thickness", "name": "film_thickness", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "find_contact_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "A", "vertices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.find_contact_point", "name": "find_contact_point", "type": null}}, "find_leftmost_valid_contact_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "A", "rotated_vertices", "rotated_upper", "angle_deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.find_leftmost_valid_contact_point", "name": "find_leftmost_valid_contact_point", "type": null}}, "find_roller_contact_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "contact_point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.find_roller_contact_point", "name": "find_roller_contact_point", "type": null}}, "geometric_center": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.geometric_center", "name": "geometric_center", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_edge_length_between_vertices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vertices", "start_vertex_idx", "end_vertex_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.get_edge_length_between_vertices", "name": "get_edge_length_between_vertices", "type": null}}, "get_film_consumption_per_layer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layer_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.get_film_consumption_per_layer", "name": "get_film_consumption_per_layer", "type": null}}, "get_intersection_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "p1", "p2", "p3", "p4"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.get_intersection_point", "name": "get_intersection_point", "type": null}}, "get_vertex_sequence_for_wrapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.get_vertex_sequence_for_wrapping", "name": "get_vertex_sequence_for_wrapping", "type": null}}, "initial_connection_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.initial_connection_length", "name": "initial_connection_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_valid_tangent_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "A", "point", "vertices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.is_valid_tangent_point", "name": "is_valid_tangent_point", "type": null}}, "layer_consumption": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.layer_consumption", "name": "layer_consumption", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_numbers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.layer_numbers", "name": "layer_numbers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.layers_count", "name": "layers_count", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "line_segments_intersect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "p1", "p2", "p3", "p4"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.line_segments_intersect", "name": "line_segments_intersect", "type": null}}, "lower_point": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.lower_point", "name": "lower_point", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "lower_point_trajectory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.lower_point_trajectory", "name": "lower_point_trajectory", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "original_vertices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.original_vertices", "name": "original_vertices", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "roller_contact_distances": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.roller_contact_distances", "name": "roller_contact_distances", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "roller_contact_points": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.roller_contact_points", "name": "roller_contact_points", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "roller_radius": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.roller_radius", "name": "roller_radius", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rotate_special_points_around_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "theta", "accumulated_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.rotate_special_points_around_center", "name": "rotate_special_points_around_center", "type": null}}, "rotate_vertices_around_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.rotate_vertices_around_center", "name": "rotate_vertices_around_center", "type": null}}, "rotate_vertices_around_center_with_custom_vertices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "theta", "custom_vertices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.rotate_vertices_around_center_with_custom_vertices", "name": "rotate_vertices_around_center_with_custom_vertices", "type": null}}, "rotation_center": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.rotation_center", "name": "rotation_center", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rotation_center_x_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.rotation_center_x_offset", "name": "rotation_center_x_offset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rotation_matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.rotation_matrix", "name": "rotation_matrix", "type": null}}, "run_simulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.run_simulation", "name": "run_simulation", "type": null}}, "set_rotation_center_x_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.set_rotation_center_x_offset", "name": "set_rotation_center_x_offset", "type": null}}, "step_angle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.step_angle", "name": "step_angle", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "theta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.theta", "name": "theta", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "theta_deg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.theta_deg", "name": "theta_deg", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "total_rotation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.total_rotation", "name": "total_rotation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_vertices_with_thickness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "accumulated_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.update_vertices_with_thickness", "name": "update_vertices_with_thickness", "type": null}}, "upper_point": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.upper_point", "name": "upper_point", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "upper_point_trajectory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.upper_point_trajectory", "name": "upper_point_trajectory", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vertices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.HexagonFilmSimulation.vertices", "name": "vertices", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "visualize_contact_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "angle_deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.HexagonFilmSimulation.visualize_contact_analysis", "name": "visualize_contact_analysis", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "main.HexagonFilmSimulation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "main.HexagonFilmSimulation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Polygon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "main.Polygon", "name": "Polygon", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "main.Polygon", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "animation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "main.animation", "name": "animation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "main.animation", "source_any": null, "type_of_any": 3}}}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.main", "name": "main", "type": null}}, "np": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "main.np", "name": "np", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "main.np", "source_any": null, "type_of_any": 3}}}, "plt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "main.plt", "name": "plt", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "main.plt", "source_any": null, "type_of_any": 3}}}}, "path": "main.py"}